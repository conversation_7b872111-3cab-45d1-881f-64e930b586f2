'use client';
import { useEffect, useRef } from 'react';
import { motion, useScroll, useTransform, useInView } from 'framer-motion';
import styles from './ContactSection.module.css';

export default function ContactSection() {
  const sectionRef = useRef(null);
  const videoRef = useRef(null);
  const isInView = useInView(sectionRef, { threshold: 0.3 });
  
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });

  const videoScale = useTransform(scrollYProgress, [0, 0.5, 1], [1.2, 1, 1.1]);
  const contentY = useTransform(scrollYProgress, [0, 0.5, 1], [100, 0, -50]);

  useEffect(() => {
    if (videoRef.current && isInView) {
      videoRef.current.play();
    }
  }, [isInView]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 60 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  return (
    <section ref={sectionRef} className={styles.section}>
      {/* Background Video */}
      <motion.div 
        className={styles.videoContainer}
        style={{ scale: videoScale }}
      >
        <video
          ref={videoRef}
          muted
          loop
          playsInline
          className={styles.backgroundVideo}
        >
          <source src="/corporate-collaboration.mp4" type="video/mp4" />
        </video>
        <div className={styles.videoOverlay} />
      </motion.div>

      {/* Content */}
      <motion.div 
        className={styles.content}
        style={{ y: contentY }}
        variants={containerVariants}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
      >
        <motion.div className={styles.header} variants={itemVariants}>
          <h2 className={styles.sectionTitle}>Work With Baltar Inc.</h2>
          <p className={styles.sectionSubtitle}>Integrated Services. Intelligent Execution.</p>
        </motion.div>

        <motion.div className={styles.ctaContainer} variants={itemVariants}>
          <p className={styles.ctaDescription}>
            Whether you're a business, homeowner, investor, or startup—Baltar offers streamlined 
            services, automated systems, and hands-on support to get you further, faster.
          </p>
          
          <div className={styles.contactGrid}>
            <motion.div className={styles.contactCard} variants={itemVariants}>
              <h3>Get Started Today</h3>
              <p>Ready to transform your business with our integrated solutions?</p>
              <a 
                href="/contact-us" 
                className={styles.primaryButton}
              >
                Contact Us
              </a>
            </motion.div>
            
            <motion.div className={styles.contactCard} variants={itemVariants}>
              <h3>Explore Our Services</h3>
              <p>Discover how our divisions can support your specific needs.</p>
              <a 
                href="/services" 
                className={styles.secondaryButton}
              >
                View All Services
              </a>
            </motion.div>
          </div>
        </motion.div>

        {/* Footer Info */}
        <motion.div className={styles.footerInfo} variants={itemVariants}>
          <div className={styles.companyInfo}>
            <h4>Baltar Inc.</h4>
            <p>One Company. Limitless Services.</p>
            <p>Serving clients across Canada with innovation and excellence.</p>
          </div>
          
          <div className={styles.quickLinks}>
            <h4>Quick Links</h4>
            <div className={styles.linkGrid}>
              <a href="/frontend-web-design" target="_blank" rel="noopener noreferrer">Web Design</a>
              <a href="/sip-and-savour" target="_blank" rel="noopener noreferrer">Hospitality</a>
              <a href="/transac" target="_blank" rel="noopener noreferrer">Transac</a>
              <a href="/vr" target="_blank" rel="noopener noreferrer">VR Fashion</a>
              <a href="/le-mode-co" target="_blank" rel="noopener noreferrer">Le Mode Co.</a>
              <a href="/consumer-pulse" target="_blank" rel="noopener noreferrer">Consumer Pulse</a>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </section>
  );
}
