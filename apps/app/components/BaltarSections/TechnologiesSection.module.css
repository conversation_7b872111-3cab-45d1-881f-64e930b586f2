/* Baltar Technologies Section - Meta Style */
.section {
  position: relative;
  min-height: 100vh;
  padding: 8rem 0;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
}

.videoContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.backgroundVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.4) contrast(1.2) saturate(1.1);
}

.videoOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 100, 200, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 50, 150, 0.2) 100%
  );
  z-index: 2;
}

.content {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.header {
  text-align: center;
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #60a5fa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sectionSubtitle {
  font-size: clamp(1rem, 2vw, 1.25rem);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.serviceCard {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2.5rem;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.serviceCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.8), transparent);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.serviceCard:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(96, 165, 250, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.serviceCard:hover::before {
  opacity: 1;
}

.cardContent h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.01em;
}

.cardContent > p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.featureList {
  list-style: none;
  padding: 0;
  margin: 1.5rem 0;
}

.featureList li {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
  position: relative;
}

.featureList li::before {
  content: '→';
  position: absolute;
  left: 0;
  color: rgba(96, 165, 250, 0.8);
  font-weight: bold;
}

.serviceDescription {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-style: italic;
  margin: 1.5rem 0;
  padding: 1rem;
  background: rgba(96, 165, 250, 0.1);
  border-left: 3px solid rgba(96, 165, 250, 0.5);
  border-radius: 4px;
}

.serviceLink {
  display: inline-flex;
  align-items: center;
  color: rgba(96, 165, 250, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.serviceLink:hover {
  color: white;
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .servicesGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .serviceCard {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .section {
    padding: 4rem 0;
  }
  
  .content {
    padding: 0 1rem;
  }
  
  .header {
    margin-bottom: 2rem;
  }
  
  .serviceCard {
    padding: 1.5rem;
  }
  
  .servicesGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .servicesGrid {
    grid-template-columns: 1fr;
  }
  
  .serviceCard {
    padding: 1.25rem;
  }
}
