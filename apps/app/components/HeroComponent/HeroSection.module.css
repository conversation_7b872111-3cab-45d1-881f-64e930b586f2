/* Meta-Inspired Hero Section Styles */
.hero {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.videoContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.heroVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.7) contrast(1.1);
}

.videoOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
  z-index: 2;
}

.heroContent {
  position: relative;
  z-index: 10;
  text-align: center;
  color: white;
  max-width: 1200px;
  padding: 0 2rem;
}

.titleContainer {
  margin-bottom: 2rem;
}

.mainTitle {
  font-size: clamp(3.5rem, 8vw, 8rem);
  font-weight: 700;
  line-height: 0.9;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #e5e7eb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 40px rgba(255, 255, 255, 0.3);
}

.subtitle {
  font-size: clamp(1.5rem, 4vw, 3rem);
  font-weight: 300;
  line-height: 1.2;
  margin-bottom: 0;
  letter-spacing: 0.01em;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

.description {
  font-size: clamp(1rem, 2vw, 1.25rem);
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  max-width: 800px;
  margin: 2rem auto 3rem;
  font-weight: 300;
  text-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
}

.scrollIndicator {
  position: absolute;
  bottom: 3rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  opacity: 0.8;
}

.scrollIndicator span {
  font-size: 0.875rem;
  font-weight: 400;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.7);
}

.arrow {
  width: 2px;
  height: 30px;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.8));
  position: relative;
  animation: bounce 2s infinite;
}

.arrow::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 8px solid rgba(255, 255, 255, 0.8);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .heroContent {
    padding: 0 1rem;
  }

  .description {
    margin: 1.5rem auto 2rem;
  }

  .scrollIndicator {
    bottom: 2rem;
  }
}

@media (max-width: 480px) {
  .titleContainer {
    margin-bottom: 1.5rem;
  }

  .description {
    margin: 1rem auto 1.5rem;
  }
}
