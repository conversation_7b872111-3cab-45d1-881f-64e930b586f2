.hero {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  background-color: #000; /* Fallback if video fails */
}

.hero-video {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  object-fit: cover;
  z-index: 0;
  opacity: 0.8; /* Slight dimming for text contrast */
}

.hero-content {
  position: relative;
  z-index: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.5); /* Black overlay */
}

.hero-content h1 {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
  max-width: 900px;
  text-shadow: 0 0 30px rgba(255, 255, 255, 0.7);
}

.hero-content p {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  max-width: 700px;
  color: #e5e7eb;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.4);
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.hero-buttons button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.375rem;
  background: white;
  color: black;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s;
}

.hero-buttons button:hover {
  background: #f3f4f6;
}

/* Responsive Typography */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 2rem;
  }

  .hero-content p {
    font-size: 1rem;
  }
}
