/* Meta-Inspired Header */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: #1c1e21;
  font-weight: 600;
  transition: opacity 0.2s ease;
}

.logo:hover {
  opacity: 0.8;
}

.logoText {
  font-size: 1.25rem;
  font-weight: 700;
  letter-spacing: -0.01em;
}

/* Navigation */
.nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.navItem {
  position: relative;
  display: flex;
  align-items: center;
}

.navLink {
  color: #1c1e21;
  font-size: 0.9rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  letter-spacing: 0.01em;
}

.navLink:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #0866ff;
}

/* Dropdown */
.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0.5rem 0;
  min-width: 220px;
  margin-top: 0.5rem;
}

.dropdownItem {
  display: block;
  padding: 0.75rem 1rem;
  color: #1c1e21;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 400;
  transition: all 0.2s ease;
  border-radius: 6px;
  margin: 0 0.5rem;
}

.dropdownItem:hover {
  background: rgba(8, 102, 255, 0.1);
  color: #0866ff;
}

/* Mobile Menu Button */
.mobileMenuButton {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 8px;
  transition: background 0.2s ease;
}

.mobileMenuButton:hover {
  background: rgba(0, 0, 0, 0.05);
}

.hamburgerLine {
  width: 20px;
  height: 2px;
  background: #1c1e21;
  margin: 2px 0;
  transition: all 0.3s ease;
  border-radius: 1px;
}

.hamburgerLine.active:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburgerLine.active:nth-child(2) {
  opacity: 0;
}

.hamburgerLine.active:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Menu */
.mobileMenu {
  background: white;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 2rem 2rem;
  overflow: hidden;
}

.mobileCategory {
  margin-bottom: 1.5rem;
}

.mobileCategoryTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: #65676b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.75rem;
}

.mobileItems {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.mobileItems .dropdownItem {
  margin: 0;
  padding: 0.75rem 0;
  border-radius: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.mobileItems .dropdownItem:last-child {
  border-bottom: none;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  .nav {
    display: none;
  }
  
  .mobileMenuButton {
    display: flex;
  }
  
  .logoText {
    font-size: 1.125rem;
  }
}

@media (max-width: 480px) {
  .container {
    height: 56px;
  }
  
  .logoText {
    display: none;
  }
}
